<UserControl x:Class="ApartmanYonetimSistemi.Views.MainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
             xmlns:views="clr-namespace:ApartmanYonetimSistemi.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1400">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <!-- View Template Selector -->
        <DataTemplate x:Key="DashboardTemplate">
            <views:DashboardView/>
        </DataTemplate>
        <DataTemplate x:Key="SitesTemplate">
            <views:SiteView/>
        </DataTemplate>
        <DataTemplate x:Key="ApartmentsTemplate">
            <views:ApartmentView/>
        </DataTemplate>
        <DataTemplate x:Key="FlatsTemplate">
            <views:FlatView/>
        </DataTemplate>
        <DataTemplate x:Key="TenantsTemplate">
            <views:TenantView/>
        </DataTemplate>
        <DataTemplate x:Key="PaymentsTemplate">
            <views:PaymentView/>
        </DataTemplate>
        <DataTemplate x:Key="ReportsTemplate">
            <views:ReportView/>
        </DataTemplate>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Top Bar -->
        <materialDesign:Card Grid.Row="0" Grid.ColumnSpan="2" 
                           materialDesign:ElevationAssist.Elevation="Dp4"
                           Background="{DynamicResource PrimaryHueMidBrush}">
            <Grid Height="70" Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo ve Başlık -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Building" 
                                           Width="32" Height="32" 
                                           Foreground="White" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,15,0"/>
                    <TextBlock Text="Apartman Yönetim Sistemi" 
                             FontSize="20" FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Site Seçici -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" 
                          HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="Aktif Site:" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="0,0,10,0"/>
                    <ComboBox ItemsSource="{Binding UserSites}"
                            SelectedItem="{Binding SelectedSite}"
                            DisplayMemberPath="SiteName"
                            MinWidth="200"
                            Style="{DynamicResource MaterialDesignComboBox}"/>
                </StackPanel>

                <!-- Kullanıcı Bilgileri -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="{Binding CurrentUser.Name}" 
                             Foreground="White" 
                             VerticalAlignment="Center"
                             Margin="0,0,15,0"/>
                    <Button Style="{DynamicResource MaterialDesignIconButton}"
                          Command="{Binding LogoutCommand}"
                          ToolTip="Çıkış Yap">
                        <materialDesign:PackIcon Kind="Logout" 
                                               Foreground="White" 
                                               Width="24" Height="24"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Sol Menü -->
        <materialDesign:Card Grid.Row="1" Grid.Column="0" 
                           materialDesign:ElevationAssist.Elevation="Dp2"
                           Margin="0,10,10,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="0,20,0,0">
                    
                    <!-- Dashboard -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowDashboardCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ViewDashboard" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Dashboard" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Siteler -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowSitesCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Domain" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Siteler" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Apartmanlar -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowApartmentsCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Building" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Apartmanlar" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Daireler -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowFlatsCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Home" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Daireler" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Kiracılar -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowTenantsCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Kiracılar" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Ödemeler -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowPaymentsCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CreditCard" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Ödemeler" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Raporlar -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowReportsCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Raporlar" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Separator Margin="10,20"/>

                    <!-- Ayarlar -->
                    <Button Style="{DynamicResource MaterialDesignFlatButton}"
                          Command="{Binding ShowSettingsCommand}"
                          HorizontalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          Height="50" Margin="10,5">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Settings" 
                                                   Width="24" Height="24" 
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,15,0"/>
                            <TextBlock Text="Ayarlar" 
                                     FontSize="16" 
                                     VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- Ana İçerik Alanı -->
        <materialDesign:Card Grid.Row="1" Grid.Column="1" 
                           materialDesign:ElevationAssist.Elevation="Dp2"
                           Margin="0,10,0,0">
            <Grid>
                <!-- Loading Overlay -->
                <Grid Background="White" 
                    Opacity="0.8"
                    Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                                   Width="50" Height="50"
                                   IsIndeterminate="True"/>
                        <TextBlock Text="Yükleniyor..." 
                                 Margin="0,20,0,0"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- Dynamic Content -->
                <ContentPresenter x:Name="MainContentPresenter"/>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
