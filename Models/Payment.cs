using System;

namespace ApartmanYonetimSistemi.Models
{
    public class Payment
    {
        public int Id { get; set; }
        public int ApartmentId { get; set; }
        public Apartment Apartment { get; set; } = null!;
        public int ResidentId { get; set; }
        public User Resident { get; set; } = null!;
        public decimal Amount { get; set; }
        public PaymentType Type { get; set; }
        public PaymentStatus Status { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime? PaidDate { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public enum PaymentType
    {
        Rent,
        Maintenance,
        Electricity,
        Water,
        Gas,
        Other
    }

    public enum PaymentStatus
    {
        Pending,
        Paid,
        Overdue,
        Cancelled
    }
} 