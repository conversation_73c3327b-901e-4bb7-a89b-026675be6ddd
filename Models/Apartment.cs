using System;

namespace ApartmanYonetimSistemi.Models
{
    public class Apartment
    {
        public int Id { get; set; }
        public string Number { get; set; } = string.Empty;
        public int Floor { get; set; }
        public int RoomCount { get; set; }
        public decimal Area { get; set; }
        public ApartmentStatus Status { get; set; }
        public int? ResidentId { get; set; }
        public User? Resident { get; set; }
        public decimal MonthlyRent { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public enum ApartmentStatus
    {
        Available,
        Occupied,
        UnderMaintenance,
        Reserved
    }
} 