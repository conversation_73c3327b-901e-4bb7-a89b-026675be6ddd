using ApartmanYonetimSistemi.Helpers;
using ApartmanYonetimSistemi.Models;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace ApartmanYonetimSistemi.ViewModels
{
    public class DashboardViewModel : ObservableObject
    {
        private User _currentUser = new();
        public User CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        private int _totalApartments;
        public int TotalApartments
        {
            get => _totalApartments;
            set => SetProperty(ref _totalApartments, value);
        }

        private int _occupiedApartments;
        public int OccupiedApartments
        {
            get => _occupiedApartments;
            set => SetProperty(ref _occupiedApartments, value);
        }

        private int _availableApartments;
        public int AvailableApartments
        {
            get => _availableApartments;
            set => SetProperty(ref _availableApartments, value);
        }

        private decimal _totalMonthlyIncome;
        public decimal TotalMonthlyIncome
        {
            get => _totalMonthlyIncome;
            set => SetProperty(ref _totalMonthlyIncome, value);
        }

        private int _pendingPayments;
        public int PendingPayments
        {
            get => _pendingPayments;
            set => SetProperty(ref _pendingPayments, value);
        }

        private ObservableCollection<Apartment> _recentApartments = new();
        public ObservableCollection<Apartment> RecentApartments
        {
            get => _recentApartments;
            set => SetProperty(ref _recentApartments, value);
        }

        private ObservableCollection<Payment> _recentPayments = new();
        public ObservableCollection<Payment> RecentPayments
        {
            get => _recentPayments;
            set => SetProperty(ref _recentPayments, value);
        }

        // Commands
        public ICommand LogoutCommand { get; }
        public ICommand RefreshCommand { get; }

        public DashboardViewModel()
        {
            LogoutCommand = new RelayCommand(ExecuteLogout);
            RefreshCommand = new RelayCommand(ExecuteRefresh);
            
            LoadDashboardData();
        }

        private void LoadDashboardData()
        {
            // Simüle edilmiş veriler
            CurrentUser = new User
            {
                Id = 1,
                FullName = "Admin Kullanıcı",
                Email = "<EMAIL>",
                Role = UserRoles.Admin
            };

            TotalApartments = 24;
            OccupiedApartments = 18;
            AvailableApartments = 6;
            TotalMonthlyIncome = 45000;
            PendingPayments = 5;

            // Örnek daireler
            RecentApartments.Clear();
            RecentApartments.Add(new Apartment { Id = 1, Number = "A1", Floor = 1, Status = ApartmentStatus.Occupied, MonthlyRent = 2500 });
            RecentApartments.Add(new Apartment { Id = 2, Number = "A2", Floor = 1, Status = ApartmentStatus.Available, MonthlyRent = 2500 });
            RecentApartments.Add(new Apartment { Id = 3, Number = "B1", Floor = 2, Status = ApartmentStatus.Occupied, MonthlyRent = 2800 });

            // Örnek ödemeler
            RecentPayments.Clear();
            RecentPayments.Add(new Payment { Id = 1, Amount = 2500, Type = PaymentType.Rent, Status = PaymentStatus.Paid, DueDate = DateTime.Now.AddDays(-5) });
            RecentPayments.Add(new Payment { Id = 2, Amount = 2800, Type = PaymentType.Rent, Status = PaymentStatus.Pending, DueDate = DateTime.Now.AddDays(5) });
            RecentPayments.Add(new Payment { Id = 3, Amount = 150, Type = PaymentType.Electricity, Status = PaymentStatus.Overdue, DueDate = DateTime.Now.AddDays(-10) });
        }

        private void ExecuteLogout(object? obj)
        {
            // Çıkış işlemi
            System.Windows.Application.Current.Shutdown();
        }

        private void ExecuteRefresh(object? obj)
        {
            LoadDashboardData();
        }
    }
} 